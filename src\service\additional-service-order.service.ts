import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ILogger } from '@midwayjs/logger';
import { BaseService } from '../common/BaseService';
import { CustomError } from '../error/custom.error';
import {
  AdditionalServiceOrder,
  AdditionalServiceOrderStatus,
} from '../entity/additional-service-order.entity';
import { AdditionalServiceOrderDetail } from '../entity/additional-service-order-detail.entity';
import { AdditionalServiceDiscountInfo } from '../entity/additional-service-discount-info.entity';
import { OrderDetail } from '../entity/order-detail.entity';
import { Order } from '../entity/order.entity';
import { Customer } from '../entity/customer.entity';
import { Employee } from '../entity/employee.entity';
import { OrderStatus } from '../common/Constant';
import { DiscountType } from '../entity/order-discount-info.entity';
import { AdditionalServiceDiscountInfoService } from './additional-service-discount-info.service';
import { AdditionalService } from '../entity';

/**
 * 创建追加服务订单数据接口
 */
export interface CreateAdditionalServiceOrderData {
  /** 订单详情ID */
  orderDetailId: number;
  /** 客户ID */
  customerId: number;
  /** 服务明细列表 */
  services: {
    serviceId: number;
    quantity: number;
  }[];
  /** 优惠信息列表 */
  discountInfos?: {
    discountType: DiscountType;
    discountId: number;
    discountAmount: number;
  }[];
  /** 备注 */
  remark?: string;
}

@Provide()
export class AdditionalServiceOrderService extends BaseService<AdditionalServiceOrder> {
  @Inject()
  ctx: Context;

  @Inject()
  logger: ILogger;

  @Inject()
  additionalServiceDiscountInfoService: AdditionalServiceDiscountInfoService;

  constructor() {
    super('追加服务订单');
  }

  getModel = () => {
    return AdditionalServiceOrder;
  };

  /**
   * 创建追加服务订单
   */
  async createAdditionalServiceOrder(data: CreateAdditionalServiceOrderData) {
    const { orderDetailId, customerId, services, discountInfos, remark } = data;

    // 验证订单详情是否存在且状态为服务中
    const orderDetail = await OrderDetail.findByPk(orderDetailId, {
      include: [
        {
          model: Order,
          include: [Customer, Employee],
        },
      ],
    });

    if (!orderDetail) {
      throw new CustomError('订单详情不存在');
    }

    if (orderDetail.order.status !== OrderStatus.服务中) {
      throw new CustomError('只有服务中的订单才能申请追加服务');
    }

    if (orderDetail.order.customerId !== customerId) {
      throw new CustomError('无权限操作此订单');
    }

    // 验证服务是否存在并计算价格
    let originalPrice = 0;
    const serviceDetails = [];

    for (const serviceItem of services) {
      const service = await AdditionalService.findByPk(serviceItem.serviceId);
      if (!service) {
        throw new CustomError(`服务ID ${serviceItem.serviceId} 不存在`);
      }
      // if (!service.published) {
      //   throw new CustomError(`服务 ${service.serviceName} 已下架`);
      // }

      const itemPrice = Number(service.price) * serviceItem.quantity;
      originalPrice += itemPrice;

      serviceDetails.push({
        serviceId: service.id,
        serviceName: service.name,
        servicePrice: service.price,
        quantity: serviceItem.quantity,
      });
    }

    // 计算优惠金额
    let cardDeduction = 0;
    let couponDeduction = 0;

    if (discountInfos && discountInfos.length > 0) {
      for (const discount of discountInfos) {
        if (discount.discountType === DiscountType.MEMBERSHIP_CARD) {
          cardDeduction += Number(discount.discountAmount);
        } else if (discount.discountType === DiscountType.COUPON) {
          couponDeduction += Number(discount.discountAmount);
        }
      }
    }

    const totalFee = originalPrice - cardDeduction - couponDeduction;

    if (totalFee < 0) {
      throw new CustomError('优惠金额不能超过订单总价');
    }

    // 生成追加服务订单编号
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0');
    const sn = `ADD${timestamp}${random}`;

    // 使用事务创建追加服务订单
    const result = await AdditionalServiceOrder.sequelize.transaction(
      async t => {
        // 创建追加服务订单
        const additionalServiceOrder = await AdditionalServiceOrder.create(
          {
            sn,
            orderDetailId,
            customerId,
            employeeId: orderDetail.order.employeeId,
            status: AdditionalServiceOrderStatus.PENDING_CONFIRM,
            originalPrice,
            totalFee,
            cardDeduction,
            couponDeduction,
            remark,
          },
          { transaction: t }
        );

        // 创建追加服务明细
        await Promise.all(
          serviceDetails.map(detail =>
            AdditionalServiceOrderDetail.create(
              {
                additionalServiceOrderId: additionalServiceOrder.id,
                ...detail,
              },
              { transaction: t }
            )
          )
        );

        return additionalServiceOrder;
      }
    );

    // 在事务外处理优惠券逻辑
    if (discountInfos && discountInfos.length > 0) {
      try {
        await this.additionalServiceDiscountInfoService.processAdditionalServiceDiscounts(
          result.id,
          discountInfos
        );
      } catch (error) {
        // 如果优惠券处理失败，需要回滚已创建的追加服务订单
        await result.destroy();
        throw error;
      }
    }

    return result;
  }

  /**
   * 员工确认追加服务
   */
  async confirmAdditionalService(id: number, employeeId: number) {
    const additionalServiceOrder = await AdditionalServiceOrder.findByPk(id, {
      include: [
        {
          model: OrderDetail,
          include: [Order],
        },
      ],
    });

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    if (
      additionalServiceOrder.status !==
      AdditionalServiceOrderStatus.PENDING_CONFIRM
    ) {
      throw new CustomError('当前状态不允许确认操作');
    }

    if (additionalServiceOrder.orderDetail.order.employeeId !== employeeId) {
      throw new CustomError('无权限操作此追加服务');
    }

    await additionalServiceOrder.update({
      status: AdditionalServiceOrderStatus.CONFIRMED,
      confirmTime: new Date(),
    });

    return true;
  }

  /**
   * 员工拒绝追加服务
   */
  async rejectAdditionalService(
    id: number,
    employeeId: number,
    rejectReason: string
  ) {
    const additionalServiceOrder = await AdditionalServiceOrder.findByPk(id, {
      include: [
        {
          model: OrderDetail,
          include: [Order],
        },
      ],
    });

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    if (
      additionalServiceOrder.status !==
      AdditionalServiceOrderStatus.PENDING_CONFIRM
    ) {
      throw new CustomError('当前状态不允许拒绝操作');
    }

    if (additionalServiceOrder.orderDetail.order.employeeId !== employeeId) {
      throw new CustomError('无权限操作此追加服务');
    }

    await additionalServiceOrder.update({
      status: AdditionalServiceOrderStatus.REJECTED,
      rejectReason,
    });

    return true;
  }

  /**
   * 支付追加服务订单
   */
  async payAdditionalServiceOrder(id: number, customerId: number) {
    const additionalServiceOrder = await AdditionalServiceOrder.findByPk(id);

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    if (additionalServiceOrder.customerId !== customerId) {
      throw new CustomError('无权限操作此追加服务订单');
    }

    if (
      additionalServiceOrder.status !== AdditionalServiceOrderStatus.CONFIRMED
    ) {
      throw new CustomError('追加服务未确认，无法支付');
    }

    await additionalServiceOrder.update({
      status: AdditionalServiceOrderStatus.PAID,
      payTime: new Date(),
    });

    // 更新主订单的追加服务信息
    await this.updateMainOrderAdditionalServiceInfo(
      additionalServiceOrder.orderDetailId
    );

    return true;
  }

  /**
   * 用户删除追加服务申请
   */
  async deleteAdditionalServiceOrder(id: number, customerId: number) {
    const additionalServiceOrder = await AdditionalServiceOrder.findByPk(id);

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    if (additionalServiceOrder.customerId !== customerId) {
      throw new CustomError('无权限操作此追加服务订单');
    }

    if (
      ![
        AdditionalServiceOrderStatus.PENDING_CONFIRM,
        AdditionalServiceOrderStatus.REJECTED,
        AdditionalServiceOrderStatus.CONFIRMED,
      ].includes(additionalServiceOrder.status)
    ) {
      throw new CustomError('只有在付款前可删除追加服务申请');
    }

    // 先处理优惠券退回
    try {
      const refundStatus =
        await this.additionalServiceDiscountInfoService.checkAdditionalServiceRefundStatus(
          id
        );
      if (refundStatus.hasDiscountInfos && !refundStatus.allRefunded) {
        await this.additionalServiceDiscountInfoService.refundAdditionalServiceDiscounts(
          id,
          '用户删除追加服务申请'
        );
      }
    } catch (error) {
      this.logger.error('处理追加服务优惠券退回失败:', error);
      // 优惠券退回失败不应该阻止删除操作，但需要记录日志
    }

    // 使用事务删除追加服务订单及其相关数据
    await AdditionalServiceOrder.sequelize.transaction(async t => {
      // 删除优惠信息
      await AdditionalServiceDiscountInfo.destroy({
        where: { additionalServiceOrderId: id },
        transaction: t,
      });

      // 删除订单明细
      await AdditionalServiceOrderDetail.destroy({
        where: { additionalServiceOrderId: id },
        transaction: t,
      });

      // 删除追加服务订单
      await additionalServiceOrder.destroy({ transaction: t });
    });

    return true;
  }

  /**
   * 退款追加服务订单（用于主订单退款时调用）
   */
  async refundAdditionalServiceOrder(
    id: number,
    reason: string,
    shouldRefundCoupons = true
  ) {
    const additionalServiceOrder = await AdditionalServiceOrder.findByPk(id);

    if (!additionalServiceOrder) {
      throw new CustomError('追加服务订单不存在');
    }

    if (additionalServiceOrder.status !== AdditionalServiceOrderStatus.PAID) {
      throw new CustomError('只能退款已支付的追加服务订单');
    }

    // 处理优惠券退回（只有在shouldRefundCoupons为true时才退回）
    if (shouldRefundCoupons) {
      try {
        const refundStatus =
          await this.additionalServiceDiscountInfoService.checkAdditionalServiceRefundStatus(
            id
          );
        if (refundStatus.hasDiscountInfos && !refundStatus.allRefunded) {
          await this.additionalServiceDiscountInfoService.refundAdditionalServiceDiscounts(
            id,
            reason
          );
        }
      } catch (error) {
        this.logger.error('处理追加服务优惠券退回失败:', error);
        throw error;
      }
    } else {
      this.logger.info(`追加服务订单${id}退款，但不退回优惠券：${reason}`);
    }

    // 更新追加服务订单状态
    await additionalServiceOrder.update({
      status: AdditionalServiceOrderStatus.REFUNDED,
    });

    // 更新主订单的追加服务信息
    await this.updateMainOrderAdditionalServiceInfo(
      additionalServiceOrder.orderDetailId
    );

    return true;
  }

  /**
   * 更新主订单的追加服务信息
   */
  private async updateMainOrderAdditionalServiceInfo(orderDetailId: number) {
    const orderDetail = await OrderDetail.findByPk(orderDetailId, {
      include: [Order],
    });

    if (!orderDetail) {
      return;
    }

    // 查询该订单所有已支付的追加服务
    const paidAdditionalServices = await AdditionalServiceOrder.findAll({
      include: [
        {
          model: OrderDetail,
          where: {
            orderId: orderDetail.orderId,
          },
        },
      ],
      where: {
        status: AdditionalServiceOrderStatus.PAID,
      },
    });

    const hasAdditionalServices = paidAdditionalServices.length > 0;
    const additionalServiceAmount = paidAdditionalServices.reduce(
      (sum, item) => sum + Number(item.totalFee),
      0
    );

    await orderDetail.order.update({
      hasAdditionalServices,
      additionalServiceAmount,
    });
  }
}
